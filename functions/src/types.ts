import { Timestamp } from "firebase-admin/firestore";

export interface UserBalance {
  sum: number;
  locked: number;
}

export interface UserEntity {
  id: string;
  email?: string | null;
  displayName?: string | null;
  photoURL?: string | null;
  role: "user" | "admin";
  tg_id?: string;
  ton_wallet_address?: string;
  raw_ton_wallet_address?: string;
  balance?: UserBalance;
  createdAt: Timestamp;
}

export interface ProductEntity {
  id: string;
  title: string;
  description: string;
  price: number;
  sellerId: string;
  category: string;
  imageUrls: string[];
  status: "active" | "sold" | "inactive";
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

export interface OrderEntity {
  id: string;
  buyerId?: string; // Optional since orders can be created without a buyer
  sellerId: string;
  productId: string;
  amount: number;
  status: "active" | "paid" | "fulfilled" | "cancelled";
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

export interface TxLookup {
  id: string;
  last_checked_record_id: string;
  updatedAt: Timestamp;
}

export interface AppConfig {
  depositFee: number; // in BPS (basis points)
  withdrawFee: number; // in BPS
  referrer_fee: number; // in BPS
  reject_order_fee: number; // in BPS
  purchase_fee: number; // in BPS
}
