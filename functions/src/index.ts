import * as admin from "firebase-admin";
import * as functions from "firebase-functions";

import { UserEntity } from "./types";
import { monitorTonTransactions } from "./ton-monitor";
import { getConfig } from "./config";

if (!admin.apps.length) {
  const config = getConfig();

  // For Firebase Functions, we need to explicitly set the service account
  // to ensure it has the necessary permissions for custom token creation
  const serviceAccount = config.firebase?.service_account_key;

  if (serviceAccount) {
    // Use service account key if provided
    admin.initializeApp({
      credential: admin.credential.cert(JSON.parse(serviceAccount)),
      projectId: config.app.project_id,
    });
    console.log("Firebase initialized with service account key");
  } else {
    // Use default credentials with explicit project ID
    admin.initializeApp({
      credential: admin.credential.applicationDefault(),
      projectId: config.app.project_id,
    });
    console.log("Firebase initialized with application default credentials");
  }
}

const db = admin.firestore();

export const createUserRecord = functions.auth.user().onCreate(async (user) => {
  const userRecord: UserEntity = {
    id: user.uid,
    email: user.email,
    displayName: user.displayName,
    photoURL: user.photoURL,
    role: "user",
    createdAt:
      admin.firestore.FieldValue.serverTimestamp() as admin.firestore.Timestamp,
  };

  try {
    await db.collection("users").doc(user.uid).set(userRecord);
    console.log(`User record created for ${user.uid}`);
  } catch (error) {
    console.error("Error creating user record:", error);
    throw new functions.https.HttpsError(
      "internal",
      (error as any).message ?? "Server error while creating user record."
    );
  }
});

export const getUserProfile = functions.https.onCall(async (data, context) => {
  if (!context.auth) {
    throw new functions.https.HttpsError(
      "unauthenticated",
      "Authentication required to get user profile."
    );
  }

  try {
    const userDoc = await db.collection("users").doc(context.auth.uid).get();

    if (!userDoc.exists) {
      throw new functions.https.HttpsError(
        "not-found",
        "User profile not found."
      );
    }

    return userDoc.data();
  } catch (error) {
    console.error("Error getting user profile:", error);
    throw new functions.https.HttpsError(
      "internal",
      (error as any).message ?? "Server error while getting user profile."
    );
  }
});

export const getUserByTelegramId = functions.https.onCall(
  async (data, context) => {
    if (!context.auth) {
      throw new functions.https.HttpsError(
        "unauthenticated",
        "Authentication required."
      );
    }

    const { tgId } = data;

    if (!tgId) {
      throw new functions.https.HttpsError(
        "invalid-argument",
        "Telegram ID is required."
      );
    }

    try {
      const usersQuery = await db
        .collection("users")
        .where("tg_id", "==", tgId)
        .limit(1)
        .get();

      if (usersQuery.empty) {
        throw new functions.https.HttpsError(
          "not-found",
          "User with this Telegram ID not found."
        );
      }

      const userDoc = usersQuery.docs[0];
      return {
        id: userDoc.id,
        ...userDoc.data(),
      };
    } catch (error) {
      console.error("Error getting user by Telegram ID:", error);
      throw new functions.https.HttpsError(
        "internal",
        (error as any).message ??
          "Server error while getting user by Telegram ID."
      );
    }
  }
);

export const tonTransactionMonitor = functions.pubsub
  .schedule("* * * * *")
  .timeZone("UTC")
  .onRun(async () => {
    try {
      console.log(
        "TON transaction monitor triggered at:",
        new Date().toISOString()
      );
      await monitorTonTransactions();
      console.log("TON transaction monitor completed successfully");
    } catch (error) {
      console.error("TON transaction monitor failed:", error);
    }
  });

export { getBalance } from "./balance-functions";

export {
  createOrder,
  makePurchase,
  rejectPurchase,
  completePurchase,
  getOrdersByProductId,
} from "./order-functions";

export { withdrawFunds } from "./withdraw-functions";

export { authenticateWithTelegram } from "./telegram-auth-functions";

export { initAppConfig } from "./init-app-config";
