import * as admin from "firebase-admin";
import * as functions from "firebase-functions";

export const initAppConfig = functions.https.onCall(async (data, context) => {
  if (!context.auth) {
    throw new functions.https.HttpsError(
      "unauthenticated",
      "Authentication required."
    );
  }

  try {
    const db = admin.firestore();
    const userDoc = await db.collection("users").doc(context.auth.uid).get();

    if (!userDoc.exists) {
      throw new functions.https.HttpsError("not-found", "User not found.");
    }

    const userData = userDoc.data();
    if (userData?.role !== "admin") {
      throw new functions.https.HttpsError(
        "permission-denied",
        "Only admin users can initialize app config."
      );
    }

    return {
      success: true,
      message: "App config initialized successfully",
    };
  } catch (error) {
    console.error("Error in initAppConfig function:", error);
    throw new functions.https.HttpsError(
      "internal",
      (error as any).message ?? "Server error while initializing app config."
    );
  }
});
