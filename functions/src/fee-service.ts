import * as admin from "firebase-admin";
import { AppConfig, UserEntity } from "./types";
import { updateUserBalance } from "./balance-service";

const APP_CONFIG_COLLECTION = "app_config";
const APP_CONFIG_DOC_ID = "fees";

export async function getAppConfig(): Promise<AppConfig | null> {
  try {
    const db = admin.firestore();
    const doc = await db
      .collection(APP_CONFIG_COLLECTION)
      .doc(APP_CONFIG_DOC_ID)
      .get();

    if (!doc.exists) {
      console.log("App config not found, using zero fees");
      return null;
    }

    return doc.data() as AppConfig;
  } catch (error) {
    console.error("Error getting app config:", error);
    throw error;
  }
}

export function calculateFeeAmount(amount: number, feeBps: number): number {
  if (!feeBps || feeBps <= 0) {
    return 0;
  }
  // BPS = basis points (1 BPS = 0.01%)
  return (amount * feeBps) / 10000;
}

export async function getAdminUser(): Promise<UserEntity | null> {
  try {
    const db = admin.firestore();
    const adminQuery = await db
      .collection("users")
      .where("role", "==", "admin")
      .limit(1)
      .get();

    if (adminQuery.empty) {
      console.error("No admin user found");
      return null;
    }

    const adminDoc = adminQuery.docs[0];
    return {
      id: adminDoc.id,
      ...adminDoc.data(),
    } as UserEntity;
  } catch (error) {
    console.error("Error finding admin user:", error);
    throw error;
  }
}

export async function applyFeeToAdmin(
  feeAmount: number,
  feeType: string
): Promise<void> {
  if (feeAmount <= 0) {
    return;
  }

  try {
    const adminUser = await getAdminUser();
    if (!adminUser) {
      console.error(`Cannot apply ${feeType} fee: no admin user found`);
      return;
    }

    await updateUserBalance(adminUser.id, feeAmount, 0);
    console.log(
      `Applied ${feeType} fee of ${feeAmount} TON to admin user ${adminUser.id}`
    );
  } catch (error) {
    console.error(`Error applying ${feeType} fee to admin:`, error);
    throw error;
  }
}

export async function applyDepositFee(
  userId: string,
  depositAmount: number
): Promise<number> {
  try {
    const config = await getAppConfig();
    if (!config?.depositFee) {
      return depositAmount;
    }

    const feeAmount = calculateFeeAmount(depositAmount, config.depositFee);
    if (feeAmount <= 0) {
      return depositAmount;
    }

    const netAmount = depositAmount - feeAmount;

    await applyFeeToAdmin(feeAmount, "deposit");

    console.log(
      `Deposit fee applied: ${feeAmount} TON (${config.depositFee} BPS), net amount: ${netAmount} TON`
    );

    return netAmount;
  } catch (error) {
    console.error("Error applying deposit fee:", error);
    throw error;
  }
}

export async function applyRejectOrderFee(
  userId: string,
  orderAmount: number
): Promise<number> {
  try {
    const config = await getAppConfig();
    if (!config?.reject_order_fee) {
      return 0;
    }

    const feeAmount = calculateFeeAmount(orderAmount, config.reject_order_fee);
    if (feeAmount <= 0) {
      return 0;
    }

    // Deduct fee from user's balance
    await updateUserBalance(userId, -feeAmount, 0);

    // Apply fee to admin account
    await applyFeeToAdmin(feeAmount, "reject_order");

    console.log(
      `Reject order fee applied: ${feeAmount} TON (${config.reject_order_fee} BPS) to user ${userId}`
    );

    return feeAmount;
  } catch (error) {
    console.error("Error applying reject order fee:", error);
    throw error;
  }
}

export async function applyPurchaseFee(
  userId: string,
  purchaseAmount: number
): Promise<number> {
  try {
    const config = await getAppConfig();
    if (!config?.purchase_fee) {
      return 0;
    }

    const feeAmount = calculateFeeAmount(purchaseAmount, config.purchase_fee);
    if (feeAmount <= 0) {
      return 0;
    }

    await updateUserBalance(userId, -feeAmount, 0);

    await applyFeeToAdmin(feeAmount, "purchase");

    console.log(
      `Purchase fee applied: ${feeAmount} TON (${config.purchase_fee} BPS) to user ${userId}`
    );

    return feeAmount;
  } catch (error) {
    console.error("Error applying purchase fee:", error);
    throw error;
  }
}

export async function applyWithdrawFee(
  userId: string,
  withdrawAmount: number
): Promise<number> {
  try {
    const config = await getAppConfig();
    if (!config?.withdrawFee) {
      return 0;
    }

    const feeAmount = calculateFeeAmount(withdrawAmount, config.withdrawFee);
    if (feeAmount <= 0) {
      return 0;
    }

    await applyFeeToAdmin(feeAmount, "withdrawal");

    console.log(
      `Withdrawal fee applied: ${feeAmount} TON (${config.withdrawFee} BPS) to user ${userId}`
    );

    return feeAmount;
  } catch (error) {
    console.error("Error applying withdrawal fee:", error);
    throw error;
  }
}
